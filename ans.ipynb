import os
from PIL import Image
import json
from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
from qwen_vl_utils import process_vision_info
import torch

base_dir = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/药品信息'
resized_dir = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/药品信息/resized_512'
model_dir = "/home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct"
os.makedirs(resized_dir, exist_ok=True)

model = Qwen2VLForConditionalGeneration.from_pretrained(model_dir, torch_dtype="auto", device_map="auto")
processor = AutoProcessor.from_pretrained(model_dir)

# <1> resize函数：将图片 resize 到合适大小 两处代码共1.5分 
def resize_image(input_path, output_path, max_size=512):
    try:
        img = Image.open(input_path)
        img = img.convert("RGB")
        img.thumbnail((max_size, max_size), Image.ANTIALIAS)
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        img.save(output_path, format='JPEG')
        return True
    except Exception as e:
        print(f"Failed to process {input_path}: {e}")
        return False

train_data = []

# <2> 读取子文件夹图片resize并找出药品正面图 两处代码共1分

for sub in os.listdir(base_dir):
    sub_path = os.path.join(base_dir, sub)
    if not os.path.isdir(sub_path):
        continue

    imgs = [fname for fname in sorted(os.listdir(sub_path))
            if fname.lower().endswith(('.jpeg'))]
    if not imgs:
        continue

    front_candidates = ['1.jpeg']
    front_name = None
    for name in imgs:
        if name in front_candidates:
            front_name = name
            break
    if front_name is None:
        front_name = imgs[0]

    
    resized_paths_all = []
    resized_front_path = None

    for name in imgs:
        orig_path = os.path.join(sub_path, name)
        
        out_sub_dir = os.path.join(resized_dir, sub)
        os.makedirs(out_sub_dir, exist_ok=True)
        out_path = os.path.join(out_sub_dir, name)
        success = resize_image(orig_path, out_path, max_size=512)
        if success:
            # uri = f"file://{out_path}"
            uri = out_path
            resized_paths_all.append(uri)
            if name == front_name:
                resized_front_path = uri

    
    if resized_front_path is None:
        print(f"子文件夹 {sub} 未能找到并 resize 出正面图片，跳过")
        continue

# <3> 构造 messages，模型多图推理 4处代码共2分
    user_instruction = "<image>请提取此药品的关键信息：名称、成分、用法用量、适应症、不良反应等。"
    content = []
    for p in resized_paths_all:
        content.append({"type": "image", "image": p})
    content.append({"type": "text", "text": user_instruction})
    messages = [
        {
            "role": "user",
            "content": content
        }
    ]
    
    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    image_inputs, video_inputs = process_vision_info(messages)
    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt"
    )
    inputs = inputs.to(model.device)
    
    with torch.no_grad():
        generated_ids = model.generate(**inputs, max_new_tokens=256, repetition_penalty=1.2, do_sample=True, temperature=0.7, top_p=0.9)
    
    generated_ids_trimmed = [
        out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]
    output_texts = processor.batch_decode(
        generated_ids_trimmed,
        skip_special_tokens=True,
        clean_up_tokenization_spaces=False
    )
    answer = output_texts[0].strip()

# <4> 构造并保存训练集，3处代码共1.5分
    entry = {
        "conversations": [
            {"from": "human", "value": user_instruction},
            {"from": "gpt", "value": answer}
        ],
        "images": [resized_front_path]
    }
    train_data.append(entry)


output_json_path = os.path.join(base_dir, 'train_data.json')
with open(output_json_path, 'w', encoding='utf-8') as f:
    json.dump(train_data, f, ensure_ascii=False, indent=2)

print(f"共处理 {len(train_data)} 个子文件夹，结果保存到 {output_json_path}")

# <5>  在 LLaMA-Factory/data/dataset_info.json 中仿照已有数据格式补充填写新生成的json数据，并将新生成的 train.json 放入 LLaMA-Factory/data 中。共1分。
"drug_info": {
    "file_name": "train_data.json",
    "formatting": "sharegpt",
    "columns": {
        "messages": "conversations",
        "images": "images"
    },
    "tags": {
        "role_tag": "from",
        "content_tag": "value",
        "user_tag": "human",
        "assistant_tag": "gpt"
    }
}

# <6>  填充examples/train_lora/qwen2_vl_lora_sft.yaml中相应参数，7处代码各0.5分，共3.5分。

### model
model_name_or_path: /home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct

### method
stage: sft
do_train: true
finetuning_type: lora
lora_rank: 8
lora_target: all

### dataset
dataset: drug_info,mllm_demo,identity,alpaca_en_demo
template: qwen2_vl
cutoff_len: 2048
max_samples: 1000
overwrite_cache: true
preprocessing_num_workers: 16
dataloader_num_workers: 4

### output
output_dir: saves/qwen2_vl-2b/lora/sft
logging_steps: 10
save_steps: 500
plot_loss: true
overwrite_output_dir: true
save_only_model: false
report_to: none  

### train
per_device_train_batch_size: 1
gradient_accumulation_steps: 8
learning_rate: 1.0e-4
num_train_epochs: 3.0
lr_scheduler_type: cosine
warmup_ratio: 0.1
bf16: true
ddp_timeout: 180000000
resume_from_checkpoint: null

### eval
# val_size: 0.1
# per_device_eval_batch_size: 1
# eval_strategy: steps
# eval_steps: 500

# <7> 开始训练 llamafactory-cli train examples/train_lora/qwen2vl_lora_sft.yaml 

# <8> 确认 examples/merge_lora/llama3_lora_sft.yaml 中相应参数，进行模型导出。0.5分

### model
model_name_or_path: /home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct
adapter_name_or_path: saves/qwen2_vl-2b/lora/sft
template: qwen2_vl
trust_remote_code: true

### export
export_dir: ans/qwen2vl_lora_sft
export_size: 5
export_device: auto  
export_legacy_format: false

# <9> 根据 Accuracy 及 规范输出数量 进行打分
# 相同分数情况下可比较规范输出数量

import numpy as np
import os
import torch
from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer
import torch.nn.functional as F

# 加载本地模型
path = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/LLaMA-Factory-0.9.1/ans/qwen2vl_lora_sft'
batch = 1
height = 392
width = 392
savepath = 'qwen2-vl-2b/qwen2_vl_2b_vision.onnx'

model = Qwen2VLForConditionalGeneration.from_pretrained(
    path,
    torch_dtype=torch.float32, # 注意此处的数据类型，由于 rknn 目前仅支持 float32 ，因此需要指定；若是在加载权重时限制了数据类型，需要自行修改config.json中的 "use_flash_attn" 参数为 false
    low_cpu_mem_usage=True,
    trust_remote_code=True).eval()
tokenizer = AutoTokenizer.from_pretrained(path, trust_remote_code=True, use_fast=False)

N = batch                           # batch size
channel = 3                                 # 3 for RGB
H = height                         # image height, must be divisible by (merge_size * patch_size)
W = width                          # image width, must be divisible by (merge_size * patch_size)
merge_size = 2
temporal_patch_size = 2
patch_size = 14
grid_t = N // temporal_patch_size if N%temporal_patch_size == 0 else N // temporal_patch_size + 1
grid_h = H // patch_size
grid_w = W // patch_size

def export_onnx_step1(image):
    if N == 1:
        images = image.repeat(temporal_patch_size, 1, 1, 1)
    elif N % temporal_patch_size != 0:
        repeat_time = temporal_patch_size - N % temporal_patch_size
        repeat_image = image[-1:, ...].repeat(repeat_time, 1, 1, 1)
        images = torch.cat((image, repeat_image), dim=0)
    patches = images.reshape(grid_t, temporal_patch_size, channel, grid_h//merge_size, merge_size, patch_size, grid_w//merge_size, merge_size, patch_size)
    patches = patches.permute(0, 3, 6, 4, 7, 2, 1, 5, 8)
    flatten_patches = patches.reshape(grid_t * grid_h * grid_w, channel * temporal_patch_size * patch_size * patch_size)
    model.visual.forward = forward_new(model.visual)
    feature = model.visual(flatten_patches, torch.tensor([grid_t, grid_h, grid_w]).unsqueeze(0))
    return feature

def export_onnx_step2(image):
    if N == 1:
        images = image.repeat(temporal_patch_size, 1, 1, 1)
    elif N % temporal_patch_size != 0:
        repeat_time = temporal_patch_size - N % temporal_patch_size
        repeat_image = image[-1:, ...].repeat(repeat_time, 1, 1, 1)
        images = torch.cat((image, repeat_image), dim=0)
    patches = images.reshape(grid_t, temporal_patch_size, channel, grid_h//merge_size, merge_size, patch_size, grid_w//merge_size, merge_size, patch_size)
    patches = patches.permute(0, 3, 6, 4, 7, 2, 1, 5, 8)
    flatten_patches = patches.reshape(grid_t * grid_h * grid_w, channel * temporal_patch_size * patch_size * patch_size)
    model.visual.forward = forward_new(model.visual)
    feature = model.visual(flatten_patches)
    return feature

def forward_new(self):
    def tmp (hidden_states, grid_thw=None):
        hidden_states = self.patch_embed(hidden_states)
        if grid_thw is not None:
            rotary_pos_emb = self.rot_pos_emb(grid_thw)
            cu_seqlens = torch.repeat_interleave(grid_thw[:, 1] * grid_thw[:, 2], grid_thw[:, 0]).cumsum(
                dim=0, dtype=torch.int32
            )
            cu_seqlens = F.pad(cu_seqlens, (1, 0), value=0)
            np.save("./rotary_pos_emb.npy", rotary_pos_emb.cpu().detach().numpy())
            np.save("./cu_seqlens.npy", cu_seqlens.cpu().detach().numpy())
        else:
            rotary_pos_emb = torch.from_numpy(np.load("./rotary_pos_emb.npy")).to(dtype=hidden_states.dtype, device=hidden_states.device)
            cu_seqlens = torch.from_numpy(np.load("./cu_seqlens.npy")).to(dtype=torch.int32, device=hidden_states.device)
        
        for blk in self.blocks:
            hidden_states = blk(hidden_states, cu_seqlens=cu_seqlens, rotary_pos_emb=rotary_pos_emb)

        return self.merger(hidden_states)
    return tmp


# 导出 Vison 部分所对应的 onnx 模型，假设输入是2x3x392x392->(28x28)x(3x2x14x14)
# pixel_values = torch.randn(784, 1176, device="cuda", dtype=torch.float32)
pixel_values = torch.randn(N, channel, H, W, device="cpu", dtype=torch.float32)
model.forward = export_onnx_step1
model = model.to(torch.float32).eval()

print("========================step1========================")
print("Generating the rotary_pos_emb and cu_seqlens done.")
feature = model(pixel_values)

# 导出 Vison 部分所对应的 onnx 模型，假设输入是2x3x392x392->(28x28)x(3x2x14x14)
# pixel_values = torch.randn(784, 1176, device="cuda", dtype=torch.float32)
pixel_values = torch.randn(N, channel, H, W, device="cpu", dtype=torch.float32)
model.forward = export_onnx_step2
model = model.to(torch.float32).eval()

print("========================step2========================")
print(f"Exporting the vision part of {path} to onnx format.")
os.makedirs(os.path.dirname(savepath), exist_ok=True)
torch.onnx.export(model, pixel_values, savepath, opset_version=19)

from rknn.api import RKNN
import numpy as np
import os

model_path = 'qwen2-vl-2b/qwen2_vl_2b_vision.onnx'
target_platform = 'rk3588'

rknn = RKNN(verbose=False)
rknn.config(target_platform=target_platform, mean_values=[[0.48145466 * 255, 0.4578275 * 255, 0.40821073 * 255]], std_values=[[0.26862954 * 255, 0.26130258 * 255, 0.27577711 * 255]])
rknn.load_onnx(model_path)
rknn.build(do_quantization=False, dataset=None)
os.makedirs("rknn", exist_ok=True)
rknn.export_rknn("./rknn/" + os.path.splitext(os.path.basename(model_path))[0] + "_{}.rknn".format(target_platform))


import torch
import os
import torchvision.transforms as T
from torchvision.transforms.functional import InterpolationMode
from PIL import Image
import json
import numpy as np
from tqdm import tqdm
from transformers import AutoModel, AutoTokenizer, AutoProcessor, Qwen2VLForConditionalGeneration

path = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/LLaMA-Factory-0.9.1/ans/qwen2vl_lora_sft'
model = Qwen2VLForConditionalGeneration.from_pretrained(
    path, torch_dtype="auto", device_map="cpu",
    low_cpu_mem_usage=True,
    trust_remote_code=True).eval()

processor = AutoProcessor.from_pretrained(path, size={"shortest_edge": 56 * 56, "longest_edge": 28 * 28 * 1280})

datasets = json.load(open("resource/data/test.json", 'r'))
for data in datasets:
    image_name = data["image"].split(".")[0]
    imgp = os.path.join(data["image_path"], data["image"])
    image = Image.open(imgp)

    conversation = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                },
                {"type": "text", "text": data["input"]},
            ],
        }
    ]
    text_prompt = processor.apply_chat_template(conversation, add_generation_prompt=True)
    inputs = processor(
        text=[text_prompt], images=[image], padding=True, return_tensors="pt"
    )
    inputs = inputs.to(model.device)
    inputs_embeds = model.model.embed_tokens(inputs["input_ids"])
    pixel_values = inputs["pixel_values"].type(model.visual.get_dtype())
    image_mask = inputs["input_ids"] == model.config.image_token_id
    image_embeds = model.visual(pixel_values, grid_thw=inputs["image_grid_thw"]).to(inputs_embeds.device)
    inputs_embeds[image_mask] = image_embeds
    print("inputs_embeds", inputs_embeds.shape)
    os.makedirs("resource/data/inputs_embeds/", exist_ok=True)
    np.save("resource/data/inputs_embeds/{}".format(image_name), inputs_embeds.to(dtype=torch.float16).cpu().detach().numpy())
    
with open('resource/data/inputs.json', 'w') as json_file:
    json_file.write('[\n')
    first = True
    for data in tqdm(datasets):
        input_embed = np.load(os.path.join("resource/data/inputs_embeds", data["image"].split(".")[0]+'.npy'))
        target = data["target"]
        input_dict = {
            "input_embed": input_embed.tolist(),
            "target": target
        }
        if not first:
            json_file.write(',\n')
        else:
            first = False
        json.dump(input_dict, json_file)
    json_file.write('\n]')

print("Done")


import os
import sys
__builtins__.exit = sys.exit

from rkllm.api import RKLLM
from datasets import load_dataset
from transformers import  AutoTokenizer
from tqdm import tqdm
import torch
from torch import nn

modelpath = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/模块C/resource/LLaMA-Factory-0.9.1/ans/qwen2vl_lora_sft'
target_platform = 'rk3588'
num_npu_core = 3
quantized_dtype = 'w8a8'
device = 'cpu'
savepath = 'qwen2_vl_2b_instruct.rkllm'
llm = RKLLM()

# Load model
# Use 'export CUDA_VISIBLE_DEVICES=2' to specify GPU device
ret = llm.load_huggingface(model=modelpath, device=device)
if ret != 0:
    print('Load model failed!')
    exit(ret)

# Build model
dataset = 'resource/data/inputs.json'

qparams = None
ret = llm.build(do_quantization=True, optimization_level=1, quantized_dtype=quantized_dtype,
                quantized_algorithm='normal', target_platform=target_platform, num_npu_core=num_npu_core, extra_qparams=qparams, dataset=dataset)

if ret != 0:
    print('Build model failed!')
    exit(ret)

# # Export rkllm model
ret = llm.export_rkllm(savepath)
if ret != 0:
    print('Export model failed!')
    exit(ret)




# <11> 每题0.5分 共2.5分