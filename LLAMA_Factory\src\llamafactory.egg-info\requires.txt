transformers<=4.46.1,>=4.41.2
datasets<=3.1.0,>=2.16.0
accelerate<=1.0.1,>=0.34.0
peft<=0.12.0,>=0.11.1
trl<=0.9.6,>=0.8.6
gradio<5.0.0,>=4.0.0
pandas>=2.0.0
scipy
einops
sentencepiece
tiktoken
protobuf
uvicorn
pydantic
fastapi
sse-starlette
matplotlib>=3.7.0
fire
packaging
pyyaml
numpy<2.0.0
av
tyro<0.9.0

[adam-mini]
adam-mini

[aqlm]
aqlm[gpu]>=1.1.0

[awq]
autoawq

[badam]
badam>=1.2.1

[bitsandbytes]
bitsandbytes>=0.39.0

[deepspeed]
deepspeed<=0.14.4,>=0.10.0

[dev]
pre-commit
ruff
pytest

[eetq]
eetq

[galore]
galore-torch

[gptq]
optimum>=1.17.0
auto-gptq>=0.5.0

[hqq]
hqq

[liger-kernel]
liger-kernel

[metrics]
nltk
jieba
rouge-chinese

[modelscope]
modelscope

[openmind]
openmind

[qwen]
transformers_stream_generator

[torch]
torch>=1.13.1

[torch-npu]
torch==2.1.0
torch-npu==2.1.0.post3
decorator

[vllm]
vllm<0.6.4,>=0.4.3
