LICENSE
MANIFEST.in
README.md
pyproject.toml
requirements.txt
setup.py
src/llamafactory/__init__.py
src/llamafactory/cli.py
src/llamafactory/launcher.py
src/llamafactory.egg-info/PKG-INFO
src/llamafactory.egg-info/SOURCES.txt
src/llamafactory.egg-info/dependency_links.txt
src/llamafactory.egg-info/entry_points.txt
src/llamafactory.egg-info/requires.txt
src/llamafactory.egg-info/top_level.txt
src/llamafactory/api/__init__.py
src/llamafactory/api/app.py
src/llamafactory/api/chat.py
src/llamafactory/api/common.py
src/llamafactory/api/protocol.py
src/llamafactory/chat/__init__.py
src/llamafactory/chat/base_engine.py
src/llamafactory/chat/chat_model.py
src/llamafactory/chat/hf_engine.py
src/llamafactory/chat/vllm_engine.py
src/llamafactory/data/__init__.py
src/llamafactory/data/aligner.py
src/llamafactory/data/collator.py
src/llamafactory/data/data_utils.py
src/llamafactory/data/formatter.py
src/llamafactory/data/loader.py
src/llamafactory/data/mm_plugin.py
src/llamafactory/data/parser.py
src/llamafactory/data/preprocess.py
src/llamafactory/data/template.py
src/llamafactory/data/tool_utils.py
src/llamafactory/data/processors/__init__.py
src/llamafactory/data/processors/feedback.py
src/llamafactory/data/processors/pairwise.py
src/llamafactory/data/processors/pretrain.py
src/llamafactory/data/processors/processor_utils.py
src/llamafactory/data/processors/supervised.py
src/llamafactory/data/processors/unsupervised.py
src/llamafactory/eval/__init__.py
src/llamafactory/eval/evaluator.py
src/llamafactory/eval/template.py
src/llamafactory/extras/__init__.py
src/llamafactory/extras/constants.py
src/llamafactory/extras/env.py
src/llamafactory/extras/logging.py
src/llamafactory/extras/misc.py
src/llamafactory/extras/packages.py
src/llamafactory/extras/ploting.py
src/llamafactory/hparams/__init__.py
src/llamafactory/hparams/data_args.py
src/llamafactory/hparams/evaluation_args.py
src/llamafactory/hparams/finetuning_args.py
src/llamafactory/hparams/generating_args.py
src/llamafactory/hparams/model_args.py
src/llamafactory/hparams/parser.py
src/llamafactory/model/__init__.py
src/llamafactory/model/adapter.py
src/llamafactory/model/loader.py
src/llamafactory/model/patcher.py
src/llamafactory/model/model_utils/__init__.py
src/llamafactory/model/model_utils/attention.py
src/llamafactory/model/model_utils/checkpointing.py
src/llamafactory/model/model_utils/embedding.py
src/llamafactory/model/model_utils/liger_kernel.py
src/llamafactory/model/model_utils/longlora.py
src/llamafactory/model/model_utils/misc.py
src/llamafactory/model/model_utils/mod.py
src/llamafactory/model/model_utils/moe.py
src/llamafactory/model/model_utils/packing.py
src/llamafactory/model/model_utils/quantization.py
src/llamafactory/model/model_utils/rope.py
src/llamafactory/model/model_utils/unsloth.py
src/llamafactory/model/model_utils/valuehead.py
src/llamafactory/model/model_utils/visual.py
src/llamafactory/train/__init__.py
src/llamafactory/train/callbacks.py
src/llamafactory/train/test_utils.py
src/llamafactory/train/trainer_utils.py
src/llamafactory/train/tuner.py
src/llamafactory/train/dpo/__init__.py
src/llamafactory/train/dpo/trainer.py
src/llamafactory/train/dpo/workflow.py
src/llamafactory/train/kto/__init__.py
src/llamafactory/train/kto/trainer.py
src/llamafactory/train/kto/workflow.py
src/llamafactory/train/ppo/__init__.py
src/llamafactory/train/ppo/ppo_utils.py
src/llamafactory/train/ppo/trainer.py
src/llamafactory/train/ppo/workflow.py
src/llamafactory/train/pt/__init__.py
src/llamafactory/train/pt/trainer.py
src/llamafactory/train/pt/workflow.py
src/llamafactory/train/rm/__init__.py
src/llamafactory/train/rm/metric.py
src/llamafactory/train/rm/trainer.py
src/llamafactory/train/rm/workflow.py
src/llamafactory/train/sft/__init__.py
src/llamafactory/train/sft/metric.py
src/llamafactory/train/sft/trainer.py
src/llamafactory/train/sft/workflow.py
src/llamafactory/webui/__init__.py
src/llamafactory/webui/chatter.py
src/llamafactory/webui/common.py
src/llamafactory/webui/css.py
src/llamafactory/webui/engine.py
src/llamafactory/webui/interface.py
src/llamafactory/webui/locales.py
src/llamafactory/webui/manager.py
src/llamafactory/webui/runner.py
src/llamafactory/webui/utils.py
src/llamafactory/webui/components/__init__.py
src/llamafactory/webui/components/chatbot.py
src/llamafactory/webui/components/data.py
src/llamafactory/webui/components/eval.py
src/llamafactory/webui/components/export.py
src/llamafactory/webui/components/infer.py
src/llamafactory/webui/components/top.py
src/llamafactory/webui/components/train.py